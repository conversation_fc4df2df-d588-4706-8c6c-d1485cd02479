"""
RT预测模型使用示例
演示如何使用RTPredictor进行训练和预测
"""

import pandas as pd
import numpy as np
from rt_prediction_model import RTPredictor
from data_analysis import RTDataAnalyzer

def load_your_data():
    """
    加载您的实际数据
    请替换此函数以加载您的真实数据
    
    Returns:
        DataFrame: 包含equip, sub_equip, capability, recipe, qty, rt列的数据
    """
    # 示例：从CSV文件加载
    # return pd.read_csv('your_data.csv')
    
    # 示例：从数据库加载
    # import sqlite3
    # conn = sqlite3.connect('your_database.db')
    # return pd.read_sql_query("SELECT * FROM rt_data", conn)
    
    # 这里使用模拟数据作为示例
    print("使用模拟数据进行演示...")
    return generate_sample_data()

def generate_sample_data(n_samples=2000):
    """生成模拟数据用于演示"""
    np.random.seed(42)
    
    # 定义可能的值
    equips = ['EQ001', 'EQ002', 'EQ003', 'EQ004', 'EQ005']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C', 'SUB_D']
    capabilities = ['CAP_HIGH', 'CAP_MED', 'CAP_LOW']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5', 'RECIPE_6']
    
    data = []
    for _ in range(n_samples):
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        qty = np.random.randint(1, 26)
        
        # 模拟复杂的rt计算逻辑
        base_rt = 8
        
        # 设备因子
        equip_factors = {
            'EQ001': 1.0, 'EQ002': 1.15, 'EQ003': 0.95, 
            'EQ004': 1.08, 'EQ005': 0.92
        }
        
        # 能力因子
        cap_factors = {
            'CAP_HIGH': 0.75, 'CAP_MED': 1.0, 'CAP_LOW': 1.35
        }
        
        # 菜单因子（模拟不同复杂度）
        recipe_factors = {
            'RECIPE_1': 0.8, 'RECIPE_2': 1.0, 'RECIPE_3': 1.2,
            'RECIPE_4': 0.9, 'RECIPE_5': 1.1, 'RECIPE_6': 1.3
        }
        
        # 数量的非线性影响
        qty_factor = 1 + (qty - 1) * 0.08 + (qty - 1) ** 1.2 * 0.002
        
        # 交互效应
        interaction_factor = 1.0
        if capability == 'CAP_HIGH' and recipe in ['RECIPE_5', 'RECIPE_6']:
            interaction_factor = 0.9  # 高能力设备处理复杂菜单更高效
        elif capability == 'CAP_LOW' and recipe in ['RECIPE_5', 'RECIPE_6']:
            interaction_factor = 1.2  # 低能力设备处理复杂菜单效率低
        
        # 添加随机噪声
        noise_factor = np.random.normal(1.0, 0.1)
        
        rt = (base_rt * equip_factors[equip] * cap_factors[capability] * 
              recipe_factors[recipe] * qty_factor * interaction_factor * noise_factor)
        
        # 确保rt为正数且合理
        rt = max(rt, 1.0)
        rt = min(rt, 200.0)  # 设置上限
        
        data.append({
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': round(rt, 2)
        })
    
    return pd.DataFrame(data)

def main():
    """主函数：完整的数据分析和模型训练流程"""
    
    print("=== RT预测模型训练流程 ===\n")
    
    # 1. 加载数据
    print("1. 加载数据...")
    df = load_your_data()
    print(f"数据加载完成，共 {len(df)} 条记录")
    print("数据预览:")
    print(df.head())
    print()
    
    # 2. 数据分析
    print("2. 进行数据分析...")
    analyzer = RTDataAnalyzer(df)
    
    # 基础统计
    analyzer.basic_statistics()
    
    # 分布分析
    analyzer.analyze_rt_distribution()
    
    # 条件组合分析
    analyzer.analyze_condition_combinations()
    
    # 相关性分析
    analyzer.correlation_analysis()
    
    # 获取建模策略建议
    strategy = analyzer.recommend_modeling_strategy()
    print()
    
    # 3. 模型训练
    print("3. 开始模型训练...")
    predictor = RTPredictor()
    
    # 根据数据量调整训练参数
    epochs = 100 if len(df) > 1000 else 50
    batch_size = 32 if len(df) > 500 else 16
    
    results = predictor.train(
        df, 
        test_size=0.2, 
        validation_split=0.2, 
        epochs=epochs, 
        batch_size=batch_size
    )
    
    # 4. 模型评估
    print("\n4. 模型评估结果:")
    print(f"平均绝对误差 (MAE): {results['mae']:.4f}")
    print(f"均方根误差 (RMSE): {results['rmse']:.4f}")
    print(f"决定系数 (R²): {results['r2']:.4f}")
    
    # 绘制训练历史
    predictor.plot_training_history()
    
    # 5. 预测示例
    print("\n5. 预测示例:")
    
    # 创建一些测试样例
    test_cases = pd.DataFrame([
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 10},
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 20},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_3', 'qty': 15},
        {'equip': 'EQ003', 'sub_equip': 'SUB_C', 'capability': 'CAP_LOW', 'recipe': 'RECIPE_5', 'qty': 25},
    ])
    
    predictions = predictor.predict(test_cases)
    
    print("测试样例预测结果:")
    for i, (_, row) in enumerate(test_cases.iterrows()):
        print(f"样例 {i+1}: {row['equip']}-{row['sub_equip']}-{row['capability']}-{row['recipe']}, qty={row['qty']} -> 预测rt={predictions[i]:.2f}")
    
    # 6. 保存模型
    print("\n6. 保存模型...")
    model_path = "rt_prediction_model"
    predictor.save_model(model_path)
    
    # 7. 模型加载测试
    print("\n7. 测试模型加载...")
    new_predictor = RTPredictor()
    new_predictor.load_model(model_path)
    
    # 验证加载的模型
    new_predictions = new_predictor.predict(test_cases)
    print("加载模型后的预测结果:")
    for i, pred in enumerate(new_predictions):
        print(f"样例 {i+1}: 预测rt={pred:.2f}")
    
    print("\n=== 训练完成 ===")
    
    return predictor, results

def predict_missing_data():
    """预测缺失数据的示例"""
    print("\n=== 预测缺失数据示例 ===")
    
    # 加载已训练的模型
    predictor = RTPredictor()
    try:
        predictor.load_model("rt_prediction_model")
    except:
        print("未找到已训练的模型，请先运行main()函数进行训练")
        return
    
    # 创建需要预测的数据（缺少rt值）
    missing_data = pd.DataFrame([
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_2', 'qty': 5},
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_2', 'qty': 12},
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_2', 'qty': 18},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_4', 'qty': 8},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_4', 'qty': 22},
    ])
    
    # 进行预测
    predictions = predictor.predict(missing_data)
    
    # 显示结果
    print("缺失数据预测结果:")
    result_df = missing_data.copy()
    result_df['predicted_rt'] = predictions
    
    for _, row in result_df.iterrows():
        print(f"{row['equip']}-{row['sub_equip']}-{row['capability']}-{row['recipe']}, qty={row['qty']} -> 预测rt={row['predicted_rt']:.2f}")
    
    return result_df

if __name__ == "__main__":
    # 运行完整的训练流程
    predictor, results = main()
    
    # 演示预测缺失数据
    predict_missing_data()
    
    print("\n使用说明:")
    print("1. 将您的真实数据替换load_your_data()函数中的模拟数据")
    print("2. 确保数据包含必需的列: equip, sub_equip, capability, recipe, qty, rt")
    print("3. 根据数据分析结果调整模型参数")
    print("4. 使用训练好的模型预测新的rt值")
