"""
RT预测模型使用示例
演示如何使用RTPredictor进行训练和预测
"""

import pandas as pd
import numpy as np
from rt_prediction_model import RTPredictor
from data_analysis import RTDataAnalyzer

def load_your_data():
    """
    加载您的实际数据
    请替换此函数以加载您的真实数据
    
    Returns:
        DataFrame: 包含equip, sub_equip, capability, recipe, qty, rt列的数据
    """
    # 示例：从CSV文件加载
    # return pd.read_csv('your_data.csv')
    
    # 示例：从数据库加载
    # import sqlite3
    # conn = sqlite3.connect('your_database.db')
    # return pd.read_sql_query("SELECT * FROM rt_data", conn)
    
    # 这里使用模拟数据作为示例
    print("Using simulated data for demonstration...")
    return generate_sample_data()

def generate_sample_data(n_samples=2000):
    """生成模拟数据用于演示"""
    np.random.seed(42)
    
    # 定义可能的值
    equips = ['EQ001', 'EQ002', 'EQ003', 'EQ004', 'EQ005']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C', 'SUB_D']
    capabilities = ['CAP_HIGH', 'CAP_MED', 'CAP_LOW']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5', 'RECIPE_6']
    
    data = []
    for _ in range(n_samples):
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        qty = np.random.randint(1, 26)
        
        # 模拟复杂的rt计算逻辑
        base_rt = 8
        
        # 设备因子
        equip_factors = {
            'EQ001': 1.0, 'EQ002': 1.15, 'EQ003': 0.95, 
            'EQ004': 1.08, 'EQ005': 0.92
        }
        
        # 能力因子
        cap_factors = {
            'CAP_HIGH': 0.75, 'CAP_MED': 1.0, 'CAP_LOW': 1.35
        }
        
        # 菜单因子（模拟不同复杂度）
        recipe_factors = {
            'RECIPE_1': 0.8, 'RECIPE_2': 1.0, 'RECIPE_3': 1.2,
            'RECIPE_4': 0.9, 'RECIPE_5': 1.1, 'RECIPE_6': 1.3
        }
        
        # 数量的非线性影响
        qty_factor = 1 + (qty - 1) * 0.08 + (qty - 1) ** 1.2 * 0.002
        
        # 交互效应
        interaction_factor = 1.0
        if capability == 'CAP_HIGH' and recipe in ['RECIPE_5', 'RECIPE_6']:
            interaction_factor = 0.9  # 高能力设备处理复杂菜单更高效
        elif capability == 'CAP_LOW' and recipe in ['RECIPE_5', 'RECIPE_6']:
            interaction_factor = 1.2  # 低能力设备处理复杂菜单效率低
        
        # 添加随机噪声
        noise_factor = np.random.normal(1.0, 0.1)
        
        rt = (base_rt * equip_factors[equip] * cap_factors[capability] * 
              recipe_factors[recipe] * qty_factor * interaction_factor * noise_factor)
        
        # 确保rt为正数且合理
        rt = max(rt, 1.0)
        rt = min(rt, 200.0)  # 设置上限
        
        data.append({
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': round(rt, 2)
        })
    
    return pd.DataFrame(data)

def main():
    """主函数：完整的数据分析和模型训练流程"""

    print("=== RT Prediction Model Training Pipeline ===\n")

    # 1. 加载数据
    print("1. Loading data...")
    df = load_your_data()
    print(f"Data loaded successfully, total {len(df)} records")
    print("Data preview:")
    print(df.head())
    print()

    # 2. 数据分析
    print("2. Performing data analysis...")
    analyzer = RTDataAnalyzer(df)
    
    # 基础统计
    analyzer.basic_statistics()
    
    # 分布分析
    analyzer.analyze_rt_distribution()
    
    # 条件组合分析
    analyzer.analyze_condition_combinations()
    
    # 相关性分析
    analyzer.correlation_analysis()
    
    # 获取建模策略建议
    strategy = analyzer.recommend_modeling_strategy()
    print()
    
    # 3. 模型训练
    print("3. Starting model training...")
    predictor = RTPredictor()

    # 根据数据量调整训练参数
    epochs = 100 if len(df) > 1000 else 50
    batch_size = 32 if len(df) > 500 else 16

    results = predictor.train(
        df,
        test_size=0.2,
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size
    )

    # 4. 模型评估
    print("\n4. Model evaluation results:")
    print(f"Mean Absolute Error (MAE): {results['mae']:.4f}")
    print(f"Root Mean Square Error (RMSE): {results['rmse']:.4f}")
    print(f"R-squared (R²): {results['r2']:.4f}")

    # 绘制训练历史
    predictor.plot_training_history()

    # 5. 预测示例
    print("\n5. Prediction examples:")
    
    # 创建一些测试样例
    test_cases = pd.DataFrame([
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 10},
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 20},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_3', 'qty': 15},
        {'equip': 'EQ003', 'sub_equip': 'SUB_C', 'capability': 'CAP_LOW', 'recipe': 'RECIPE_5', 'qty': 25},
    ])
    
    predictions = predictor.predict(test_cases)

    print("Test case prediction results:")
    for i, (_, row) in enumerate(test_cases.iterrows()):
        print(f"Case {i+1}: {row['equip']}-{row['sub_equip']}-{row['capability']}-{row['recipe']}, qty={row['qty']} -> predicted rt={predictions[i]:.2f}")

    # 6. 保存模型
    print("\n6. Saving model...")
    model_path = "rt_prediction_model"
    predictor.save_model(model_path)

    # 7. 模型加载测试
    print("\n7. Testing model loading...")
    new_predictor = RTPredictor()
    new_predictor.load_model(model_path)

    # 验证加载的模型
    new_predictions = new_predictor.predict(test_cases)
    print("Prediction results after loading model:")
    for i, pred in enumerate(new_predictions):
        print(f"Case {i+1}: predicted rt={pred:.2f}")

    print("\n=== Training Complete ===")
    
    return predictor, results

def predict_missing_qty_for_conditions(df, predictor, target_conditions=None, export_path="prediction_results.csv"):
    """
    为指定条件组合预测缺失片数的rt值

    Args:
        df: 原始训练数据
        predictor: 已训练的预测器
        target_conditions: 目标条件组合列表，如果为None则处理所有组合
        export_path: 导出文件路径

    Returns:
        包含实际值和预测值的完整DataFrame
    """
    print("\n=== Predicting Missing Qty for Condition Combinations ===")

    # 定义完整的qty范围
    full_qty_range = list(range(1, 26))  # 1到25

    # 如果没有指定目标条件，则处理所有条件组合
    if target_conditions is None:
        condition_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        df['condition_key'] = df[condition_cols].astype(str).agg('_'.join, axis=1)
        unique_conditions = df.groupby(condition_cols).size().reset_index(name='count')
        target_conditions = unique_conditions[condition_cols].to_dict('records')

    all_results = []

    for i, condition in enumerate(target_conditions):
        print(f"\nProcessing condition {i+1}/{len(target_conditions)}: {condition}")

        # 获取该条件下的现有数据
        condition_mask = True
        for col, val in condition.items():
            condition_mask &= (df[col] == val)

        existing_data = df[condition_mask].copy()
        existing_qtys = set(existing_data['qty'].tolist())
        missing_qtys = [qty for qty in full_qty_range if qty not in existing_qtys]

        print(f"  Existing qtys: {sorted(existing_qtys)}")
        print(f"  Missing qtys: {sorted(missing_qtys)}")

        # 为现有数据添加标记
        for _, row in existing_data.iterrows():
            result_row = {
                'equip': row['equip'],
                'sub_equip': row['sub_equip'],
                'capability': row['capability'],
                'recipe': row['recipe'],
                'qty': row['qty'],
                'rt': row['rt'],
                'result_type': 'actual'
            }
            all_results.append(result_row)

        # 预测缺失的qty
        if missing_qtys:
            missing_data = pd.DataFrame([
                {**condition, 'qty': qty} for qty in missing_qtys
            ])

            try:
                predictions = predictor.predict(missing_data)
                print(f"  Predicted {len(predictions)} missing values")

                # 添加预测结果
                for qty, pred_rt in zip(missing_qtys, predictions):
                    result_row = {
                        'equip': condition['equip'],
                        'sub_equip': condition['sub_equip'],
                        'capability': condition['capability'],
                        'recipe': condition['recipe'],
                        'qty': qty,
                        'rt': pred_rt,
                        'result_type': 'predicted'
                    }
                    all_results.append(result_row)

            except Exception as e:
                print(f"  Error predicting for condition {condition}: {e}")

    # 创建完整结果DataFrame
    result_df = pd.DataFrame(all_results)
    result_df = result_df.sort_values(['equip', 'sub_equip', 'capability', 'recipe', 'qty']).reset_index(drop=True)

    # 导出结果
    result_df.to_csv(export_path, index=False)
    print(f"\nResults exported to: {export_path}")

    # 显示统计信息
    actual_count = (result_df['result_type'] == 'actual').sum()
    predicted_count = (result_df['result_type'] == 'predicted').sum()

    print(f"\nSummary:")
    print(f"  Total records: {len(result_df)}")
    print(f"  Actual values: {actual_count}")
    print(f"  Predicted values: {predicted_count}")
    print(f"  Condition combinations: {len(target_conditions)}")

    return result_df

def predict_missing_data():
    """预测缺失数据的示例 - 更新为使用新的功能"""
    print("\n=== Missing Data Prediction Example ===")

    # 加载已训练的模型
    predictor = RTPredictor()
    try:
        predictor.load_model("rt_prediction_model")
    except:
        print("Trained model not found, please run main() function first for training")
        return

    # 加载训练数据来获取现有的条件组合
    df = load_your_data()

    # 示例：为特定条件组合预测缺失片数
    target_conditions = [
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1'},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_2'},
    ]

    # 预测并导出结果
    result_df = predict_missing_qty_for_conditions(
        df, predictor, target_conditions, "missing_qty_predictions.csv"
    )

    # 显示部分结果
    print("\nSample results:")
    for condition in target_conditions:
        condition_mask = True
        for col, val in condition.items():
            condition_mask &= (result_df[col] == val)

        condition_results = result_df[condition_mask].sort_values('qty')
        print(f"\nCondition: {condition}")
        print(condition_results[['qty', 'rt', 'result_type']].to_string(index=False))

    return result_df

def predict_specific_condition_missing_qty(equip, sub_equip, capability, recipe,
                                          existing_qtys, existing_rts,
                                          predictor, export_path=None):
    """
    为特定条件组合预测缺失片数的rt值

    Args:
        equip, sub_equip, capability, recipe: 条件组合
        existing_qtys: 现有的片数列表
        existing_rts: 对应的rt值列表
        predictor: 已训练的预测器
        export_path: 导出文件路径，如果为None则自动生成

    Returns:
        包含实际值和预测值的完整DataFrame
    """
    print(f"\n=== Predicting Missing Qty for Specific Condition ===")
    print(f"Condition: {equip}-{sub_equip}-{capability}-{recipe}")

    # 验证输入数据
    if len(existing_qtys) != len(existing_rts):
        raise ValueError("existing_qtys and existing_rts must have the same length")

    # 定义完整的qty范围
    full_qty_range = list(range(1, 26))  # 1到25
    existing_qtys_set = set(existing_qtys)
    missing_qtys = [qty for qty in full_qty_range if qty not in existing_qtys_set]

    print(f"Existing qtys: {sorted(existing_qtys)}")
    print(f"Missing qtys: {sorted(missing_qtys)}")

    all_results = []

    # 添加现有数据
    for qty, rt in zip(existing_qtys, existing_rts):
        result_row = {
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': rt,
            'result_type': 'actual'
        }
        all_results.append(result_row)

    # 预测缺失的qty
    if missing_qtys:
        missing_data = pd.DataFrame([
            {
                'equip': equip,
                'sub_equip': sub_equip,
                'capability': capability,
                'recipe': recipe,
                'qty': qty
            } for qty in missing_qtys
        ])

        try:
            predictions = predictor.predict(missing_data)
            print(f"Successfully predicted {len(predictions)} missing values")

            # 添加预测结果
            for qty, pred_rt in zip(missing_qtys, predictions):
                result_row = {
                    'equip': equip,
                    'sub_equip': sub_equip,
                    'capability': capability,
                    'recipe': recipe,
                    'qty': qty,
                    'rt': pred_rt,
                    'result_type': 'predicted'
                }
                all_results.append(result_row)

        except Exception as e:
            print(f"Error predicting missing values: {e}")
            return None

    # 创建结果DataFrame
    result_df = pd.DataFrame(all_results)
    result_df = result_df.sort_values('qty').reset_index(drop=True)

    # 导出结果
    if export_path is None:
        export_path = f"prediction_{equip}_{sub_equip}_{capability}_{recipe}.csv"

    result_df.to_csv(export_path, index=False)
    print(f"Results exported to: {export_path}")

    # 显示结果
    print(f"\nComplete results for {equip}-{sub_equip}-{capability}-{recipe}:")
    print("="*80)
    print(f"{'Qty':<5} {'RT':<10} {'Type':<10}")
    print("-"*25)

    for _, row in result_df.iterrows():
        qty = row['qty']
        rt = row['rt']
        result_type = row['result_type']
        print(f"{qty:<5} {rt:<10.2f} {result_type:<10}")

    # 统计信息
    actual_count = (result_df['result_type'] == 'actual').sum()
    predicted_count = (result_df['result_type'] == 'predicted').sum()

    print(f"\nSummary:")
    print(f"  Total records: {len(result_df)}")
    print(f"  Actual values: {actual_count}")
    print(f"  Predicted values: {predicted_count}")
    print(f"  Coverage: {len(result_df)}/25 qty values ({len(result_df)/25*100:.1f}%)")

    return result_df

def example_specific_prediction():
    """使用示例：为特定条件组合预测缺失片数"""
    print("\n=== Example: Specific Condition Prediction ===")

    # 加载已训练的模型
    predictor = RTPredictor()
    try:
        predictor.load_model("rt_prediction_model")
    except:
        print("Trained model not found, please run main() function first for training")
        return

    # 示例数据：某个条件组合下的现有数据
    # 假设这是您提到的情况：有qty 1,2,3,4,6,8,9,10,15,16,17,18,19,20,25的数据
    existing_qtys = [1, 2, 3, 4, 6, 8, 9, 10, 15, 16, 17, 18, 19, 20, 25]
    existing_rts = [8.5, 9.2, 10.1, 11.3, 13.8, 16.2, 17.1, 18.5, 25.8, 27.2, 28.6, 30.1, 31.5, 33.2, 42.1]

    # 预测缺失的片数：5, 7, 11, 12, 13, 14, 21, 22, 23, 24
    result_df = predict_specific_condition_missing_qty(
        equip='EQ001',
        sub_equip='SUB_A',
        capability='CAP_HIGH',
        recipe='RECIPE_1',
        existing_qtys=existing_qtys,
        existing_rts=existing_rts,
        predictor=predictor,
        export_path="specific_condition_prediction.csv"
    )

    return result_df

if __name__ == "__main__":
    # 运行完整的训练流程
    predictor, results = main()

    # 演示预测缺失数据
    predict_missing_data()

    # 演示特定条件组合的预测
    example_specific_prediction()

    print("\nUsage Instructions:")
    print("1. Replace the simulated data in load_your_data() function with your real data")
    print("2. Ensure data contains required columns: equip, sub_equip, capability, recipe, qty, rt")
    print("3. Adjust model parameters based on data analysis results")
    print("4. Use predict_specific_condition_missing_qty() for specific condition combinations")
    print("5. Use predict_missing_qty_for_conditions() for batch processing multiple conditions")
    print("6. Check the exported CSV files for complete results with actual/predicted labels")
