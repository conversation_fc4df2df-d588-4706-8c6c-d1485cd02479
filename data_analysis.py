import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class RTDataAnalyzer:
    """
    运行时间数据分析工具
    用于分析数据分布、相关性和决定建模策略
    """
    
    def __init__(self, df: pd.DataFrame):
        """
        初始化分析器
        
        Args:
            df: 包含equip, sub_equip, capability, recipe, qty, rt列的DataFrame
        """
        self.df = df.copy()
        self.condition_stats = None
        
    def basic_statistics(self):
        """基础统计分析"""
        print("=== 基础统计信息 ===")
        print(f"数据总量: {len(self.df)} 条记录")
        print(f"数据时间范围: qty={self.df['qty'].min()}-{self.df['qty'].max()}, rt={self.df['rt'].min():.2f}-{self.df['rt'].max():.2f}")
        print("\n各列基础统计:")
        print(self.df.describe())
        
        print("\n分类变量分布:")
        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        for col in categorical_cols:
            print(f"\n{col}:")
            print(self.df[col].value_counts())
    
    def analyze_rt_distribution(self):
        """分析rt分布特征"""
        print("\n=== RT分布分析 ===")
        
        # 整体分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 直方图
        axes[0,0].hist(self.df['rt'], bins=50, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('RT分布直方图')
        axes[0,0].set_xlabel('RT')
        axes[0,0].set_ylabel('频次')
        
        # Q-Q图检验正态性
        stats.probplot(self.df['rt'], dist="norm", plot=axes[0,1])
        axes[0,1].set_title('RT正态性检验 (Q-Q图)')
        
        # 箱线图按qty分组
        qty_groups = pd.cut(self.df['qty'], bins=5, labels=['1-5', '6-10', '11-15', '16-20', '21-25'])
        self.df['qty_group'] = qty_groups
        sns.boxplot(data=self.df, x='qty_group', y='rt', ax=axes[1,0])
        axes[1,0].set_title('不同qty范围的RT分布')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # RT vs qty散点图
        axes[1,1].scatter(self.df['qty'], self.df['rt'], alpha=0.6)
        axes[1,1].set_xlabel('qty')
        axes[1,1].set_ylabel('rt')
        axes[1,1].set_title('RT vs qty散点图')
        
        plt.tight_layout()
        plt.show()
        
        # 统计检验
        shapiro_stat, shapiro_p = stats.shapiro(self.df['rt'].sample(min(5000, len(self.df))))
        print(f"Shapiro-Wilk正态性检验: 统计量={shapiro_stat:.4f}, p值={shapiro_p:.4f}")
        if shapiro_p < 0.05:
            print("RT分布显著偏离正态分布，建议考虑数据变换")
        else:
            print("RT分布接近正态分布")
    
    def analyze_condition_combinations(self):
        """分析不同条件组合的统计特征"""
        print("\n=== 条件组合分析 ===")
        
        # 创建条件组合
        condition_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        self.df['condition_key'] = self.df[condition_cols].astype(str).agg('_'.join, axis=1)
        
        # 计算每个组合的统计信息
        self.condition_stats = self.df.groupby('condition_key').agg({
            'rt': ['count', 'mean', 'median', 'std', 'min', 'max'],
            'qty': ['min', 'max', 'nunique']
        }).round(4)
        
        self.condition_stats.columns = ['_'.join(col).strip() for col in self.condition_stats.columns]
        self.condition_stats = self.condition_stats.reset_index()
        
        print(f"总共有 {len(self.condition_stats)} 种不同的条件组合")
        print(f"平均每种组合有 {self.condition_stats['rt_count'].mean():.1f} 条记录")
        
        # 显示数据量最多和最少的组合
        print("\n数据量最多的5种组合:")
        top_combinations = self.condition_stats.nlargest(5, 'rt_count')
        print(top_combinations[['condition_key', 'rt_count', 'rt_mean', 'rt_std']])
        
        print("\n数据量最少的5种组合:")
        bottom_combinations = self.condition_stats.nsmallest(5, 'rt_count')
        print(bottom_combinations[['condition_key', 'rt_count', 'rt_mean', 'rt_std']])
        
        # 可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 每种组合的数据量分布
        axes[0,0].hist(self.condition_stats['rt_count'], bins=20, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('每种条件组合的数据量分布')
        axes[0,0].set_xlabel('数据量')
        axes[0,0].set_ylabel('组合数量')
        
        # RT均值的分布
        axes[0,1].hist(self.condition_stats['rt_mean'], bins=20, alpha=0.7, edgecolor='black')
        axes[0,1].set_title('各组合RT均值分布')
        axes[0,1].set_xlabel('RT均值')
        axes[0,1].set_ylabel('组合数量')
        
        # RT标准差的分布
        axes[1,0].hist(self.condition_stats['rt_std'].dropna(), bins=20, alpha=0.7, edgecolor='black')
        axes[1,0].set_title('各组合RT标准差分布')
        axes[1,0].set_xlabel('RT标准差')
        axes[1,0].set_ylabel('组合数量')
        
        # 数据量 vs RT变异性
        valid_std = self.condition_stats['rt_std'].notna()
        axes[1,1].scatter(self.condition_stats.loc[valid_std, 'rt_count'], 
                         self.condition_stats.loc[valid_std, 'rt_std'], alpha=0.6)
        axes[1,1].set_xlabel('数据量')
        axes[1,1].set_ylabel('RT标准差')
        axes[1,1].set_title('数据量 vs RT变异性')
        
        plt.tight_layout()
        plt.show()
    
    def recommend_modeling_strategy(self):
        """基于数据分析推荐建模策略"""
        print("\n=== 建模策略建议 ===")
        
        if self.condition_stats is None:
            self.analyze_condition_combinations()
        
        # 分析数据特征
        total_combinations = len(self.condition_stats)
        avg_samples_per_combination = self.condition_stats['rt_count'].mean()
        combinations_with_few_samples = (self.condition_stats['rt_count'] < 5).sum()
        high_variance_combinations = (self.condition_stats['rt_std'] > self.condition_stats['rt_std'].quantile(0.8)).sum()
        
        print(f"数据特征分析:")
        print(f"- 总条件组合数: {total_combinations}")
        print(f"- 平均每组合样本数: {avg_samples_per_combination:.1f}")
        print(f"- 样本数<5的组合: {combinations_with_few_samples} ({combinations_with_few_samples/total_combinations*100:.1f}%)")
        print(f"- 高方差组合数: {high_variance_combinations} ({high_variance_combinations/total_combinations*100:.1f}%)")
        
        print(f"\n建模策略建议:")
        
        # 策略1: 数据使用建议
        if avg_samples_per_combination >= 10:
            print("✓ 建议使用原始数据训练，数据量充足")
        else:
            print("⚠ 建议结合原始数据和统计特征，部分组合数据量较少")
        
        # 策略2: 特征工程建议
        if combinations_with_few_samples / total_combinations > 0.3:
            print("✓ 建议添加历史统计特征(均值、中位数等)作为补充")
            print("✓ 考虑使用层次化建模或迁移学习")
        
        if high_variance_combinations / total_combinations > 0.2:
            print("✓ 建议添加不确定性估计(如置信区间)")
            print("✓ 考虑使用集成方法提高鲁棒性")
        
        # 策略3: 模型复杂度建议
        if total_combinations > 100:
            print("✓ 条件组合较多，建议使用深度学习模型")
        else:
            print("✓ 可以考虑传统机器学习方法(如随机森林、XGBoost)")
        
        # 策略4: 验证策略建议
        print("✓ 建议使用分层抽样确保各条件组合在训练/测试集中均有代表")
        print("✓ 建议进行交叉验证评估模型稳定性")
        
        return {
            'use_raw_data': avg_samples_per_combination >= 10,
            'add_statistical_features': combinations_with_few_samples / total_combinations > 0.3,
            'add_uncertainty': high_variance_combinations / total_combinations > 0.2,
            'use_deep_learning': total_combinations > 100,
            'avg_samples_per_combination': avg_samples_per_combination
        }
    
    def correlation_analysis(self):
        """相关性分析"""
        print("\n=== 相关性分析 ===")
        
        # 编码分类变量进行相关性分析
        df_encoded = self.df.copy()
        from sklearn.preprocessing import LabelEncoder
        
        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        for col in categorical_cols:
            le = LabelEncoder()
            df_encoded[f'{col}_encoded'] = le.fit_transform(df_encoded[col].astype(str))
        
        # 计算相关性矩阵
        numeric_cols = ['equip_encoded', 'sub_equip_encoded', 'capability_encoded', 
                       'recipe_encoded', 'qty', 'rt']
        corr_matrix = df_encoded[numeric_cols].corr()
        
        # 可视化相关性矩阵
        plt.figure(figsize=(10, 8))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                   square=True, linewidths=0.5)
        plt.title('特征相关性矩阵')
        plt.tight_layout()
        plt.show()
        
        # 分析与rt的相关性
        rt_correlations = corr_matrix['rt'].abs().sort_values(ascending=False)
        print("与RT的相关性排序:")
        for feature, corr in rt_correlations.items():
            if feature != 'rt':
                print(f"{feature}: {corr:.4f}")


# 使用示例
if __name__ == "__main__":
    # 使用rt_prediction_model.py中的示例数据生成代码
    import sys
    sys.path.append('.')
    
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    equips = ['EQ001', 'EQ002', 'EQ003', 'EQ004']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C']
    capabilities = ['CAP_HIGH', 'CAP_MED', 'CAP_LOW']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5']
    
    data = []
    for _ in range(n_samples):
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        qty = np.random.randint(1, 26)
        
        # 模拟rt与各因素的关系
        base_rt = 10
        equip_factor = {'EQ001': 1.0, 'EQ002': 1.2, 'EQ003': 0.9, 'EQ004': 1.1}[equip]
        cap_factor = {'CAP_HIGH': 0.8, 'CAP_MED': 1.0, 'CAP_LOW': 1.3}[capability]
        recipe_factor = np.random.uniform(0.8, 1.5)
        qty_factor = qty * 0.1 + np.random.normal(0, 0.1)
        
        rt = base_rt * equip_factor * cap_factor * recipe_factor * qty_factor
        rt = max(rt, 1)
        
        data.append({
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': rt
        })
    
    df = pd.DataFrame(data)
    
    # 进行数据分析
    analyzer = RTDataAnalyzer(df)
    analyzer.basic_statistics()
    analyzer.analyze_rt_distribution()
    analyzer.analyze_condition_combinations()
    analyzer.correlation_analysis()
    strategy = analyzer.recommend_modeling_strategy()
    
    print(f"\n推荐的建模策略: {strategy}")
